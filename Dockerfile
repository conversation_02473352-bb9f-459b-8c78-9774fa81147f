# Build stage
FROM rust:1.88.0 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/warda

# Copy manifests
COPY Cargo.toml Cargo.lock ./

# Create dummy lib.rs and build dependencies only
RUN mkdir -p src \
    && echo "fn main() {}" > src/main.rs \
    && echo "pub mod app;" > src/lib.rs \
    && cargo build --release \
    && rm -rf src

# Copy source code
COPY . .

# Build for release
RUN cargo build --release

# Runtime stage
FROM debian:bullseye-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libssl1.1 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /usr/src/warda/target/release/warda .

# Copy configuration
COPY .env.example .env

# Expose port
EXPOSE 8080

# Set environment variables
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1

# Run the application
CMD ["./warda"]
