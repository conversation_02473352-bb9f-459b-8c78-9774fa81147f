# Warda Developer Onboarding

## Prerequisites
- [Docker](https://docs.docker.com/get-docker/)
- [<PERSON><PERSON><PERSON>](https://minikube.sigs.k8s.io/docs/)
- [Helm](https://helm.sh/docs/)
- [Rust](https://rustup.rs/)
- [kubectl](https://kubernetes.io/docs/tasks/tools/)

## Quick Start

1. **Start Minikube and deploy the stack:**
   ```sh
   make onboard
   ```
   This will:
   - Start Minikube and required addons
   - Build and deploy all services
   - Print out the required `/etc/hosts` entries

2. **Add hosts entries:**
   After running `make onboard`, add the following line to your `/etc/hosts` file:
   ```
   <MINIKUBE_IP> frontend.local keycloak.local
   ```
   Replace `<MINIKUBE_IP>` with the IP shown in the output, e.g.:
   ```
   ************ frontend.local keycloak.local
   ```

3. **Access the application:**
   - Frontend: [http://frontend.local](http://frontend.local)
   - Keycloak Admin: [http://keycloak.local/admin](http://keycloak.local/admin)

   Default Keycloak admin credentials:
   - Username: `admin`
   - Password: `admin123`

4. **First login:**
   - The app will redirect you to Keycloak for authentication.
   - After logging in, you'll be redirected back to the frontend.

## Frontend Development

The frontend can be built and run in two ways:

### Native Desktop Application
For local development and testing:
```sh
# Build for native desktop
make build-native

# Run natively (opens desktop window)
make run-native

# Development mode with hot reload
make dev-native
```

### WASM/Browser Application
For web deployment:
```sh
# Build for WASM/browser
make build-wasm
```

The native version runs without authentication (for development), while the WASM version integrates with Keycloak authentication when deployed.

## Useful Makefile Targets
- `make help` — Show all available commands
- `make onboard` — Full setup for new developers
- `make print-hosts` — Print the required `/etc/hosts` entries
- `make keycloak-logs` — Show recent Keycloak logs
- `make build-native` — Build frontend for native desktop
- `make run-native` — Run frontend natively
- `make build-wasm` — Build frontend for WASM/browser

## Troubleshooting
- If you see a redirect_uri error, make sure you're accessing the app via `http://frontend.local` and your `/etc/hosts` entry is correct.
- If you change any ingress or hostname, update the allowed redirect URIs in `helm/warda/values.yaml` for Keycloak.

---

Happy hacking!
