use std::sync::Arc;

use axum::{
    body::Body,
    http::{header, Request, StatusCode},
    Router,
};
use serde::de::DeserializeOwned;
use sqlx::{Pool, Postgres};
use tower::ServiceExt;

use crate::{
    app::{create_app, AppState},
    config::AppConfig,
    db::pool::DbPool,
    models::user::{CreateUserRequest, UserRole},
    repositories::user_repository::UserRepository,
    services::{auth_service::AuthService, entity_service::EntityService},
};

/// TestApp holds the test application state
pub struct TestApp {
    pub app: Router,
    pub db_pool: Pool<Postgres>,
    pub config: AppConfig,
    pub auth_service: Arc<AuthService>,
    pub entity_service: Arc<dyn EntityService>,
}

impl TestApp {
    /// Create a new test application with a fresh database
    pub async fn new() -> Self {
        // Load test configuration
        let config = AppConfig::test_config();
        
        // Set up test database
        let db_pool = DbPool::new(&config.database).await.unwrap();
        
        // Run migrations
        db_pool.run_migrations().await.unwrap();
        
        // Initialize repositories
        let user_repo = Arc::new(UserRepository::new(db_pool.clone()));
        
        // Initialize services
        let auth_service = Arc::new(AuthService::new(user_repo, config.clone()));
        
        // Initialize schema registry
        let schema_registry = Arc::new(crate::schema_registry::SchemaRegistry::new());
        
        // Initialize entity service
        let entity_service = crate::services::entity_service::create_entity_service(
            db_pool.clone(),
            schema_registry,
        );
        
        // Create test user
        let _ = auth_service
            .register(&CreateUserRequest {
                username: "testuser".to_string(),
                email: "<EMAIL>".to_string(),
                password: "password123".to_string(),
                role: Some(UserRole::Admin),
            })
            .await
            .expect("Failed to create test user");
        
        // Create the application
        let app = create_app(config.clone(), db_pool.clone());
        
        Self {
            app,
            db_pool,
            config,
            auth_service,
            entity_service,
        }
    }
    
    /// Get a reference to the application router
    pub fn router(&self) -> Router {
        self.app.clone()
    }
    
    /// Create an authenticated request with a JWT token
    pub async fn authenticated_request(&self, method: &str, uri: &str) -> Request<Body> {
        let token = self
            .auth_service
            .login("testuser", "password123")
            .await
            .expect("Failed to login test user")
            .access_token;
            
        Request::builder()
            .method(method)
            .uri(uri)
            .header(header::AUTHORIZATION, format!("Bearer {}", token))
            .header(header::CONTENT_TYPE, "application/json")
            .body(Body::empty())
            .unwrap()
    }
    
    /// Send a request and parse the JSON response
    pub async fn send_request<T: DeserializeOwned>(
        &self,
        request: Request<Body>,
    ) -> (StatusCode, T) {
        let response = self.router().oneshot(request).await.unwrap();
        let status = response.status();
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let json: T = serde_json::from_slice(&body).unwrap_or_else(|_| {
            panic!(
                "Failed to parse response as JSON: {}",
                String::from_utf8_lossy(&body)
            )
        });
        (status, json)
    }
}

impl Default for TestApp {
    fn default() -> Self {
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(Self::new())
    }
}

/// Test configuration for the application
pub fn test_config() -> AppConfig {
    // Create a test configuration with a unique database name
    let database_url = format!(
        "postgres://postgres:postgres@localhost:5432/test_{}",
        uuid::Uuid::new_v4()
    );
    
    std::env::set_var("DATABASE_URL", &database_url);
    
    AppConfig {
        database: crate::config::DatabaseConfig {
            url: database_url,
            max_connections: 5,
        },
        server: crate::config::ServerConfig {
            port: 0, // Use random port for tests
            host: "127.0.0.1".to_string(),
            log_level: "debug".to_string(),
        },
        auth: crate::config::AuthConfig {
            jwt_secret: "test-secret".to_string(),
            jwt_expiry_minutes: 60,
            refresh_token_secret: "test-refresh-secret".to_string(),
            refresh_token_expiry_days: 7,
        },
    }
}
