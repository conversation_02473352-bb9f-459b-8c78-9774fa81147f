use serde_json::json;
use uuid::Uuid;

use crate::{
    models::{
        entity::{CreateEntityRequest, EntityResponse, EntityType, UpdateEntityRequest},
        PaginatedResponse,
    },
    test_utils::TestApp,
};

#[tokio::test]
async fn test_create_entity() {
    // Setup test app
    let app = TestApp::new().await;
    
    // Create entity request
    let create_request = CreateEntityRequest {
        name: "Test Entity".to_string(),
        entity_type: EntityType::Person,
        description: Some("Test Description".to_string()),
        attributes: None,
        schema_id: None,
    };
    
    // Send request
    let request = app
        .authenticated_request(
            "POST",
            "/api/entities",
        )
        .await
        .json(&create_request);
    
    let (status, response): (_, EntityResponse) = app.send_request(request).await;
    
    // Assert response
    assert_eq!(status, 201);
    assert_eq!(response.name, "Test Entity");
    assert_eq!(response.entity_type, EntityType::Person);
    assert_eq!(response.description, Some("Test Description".to_string()));
}

#[tokio::test]
async fn test_get_entity() {
    // Setup test app
    let app = TestApp::new().await;
    
    // First create an entity
    let create_request = CreateEntityRequest {
        name: "Test Get Entity".to_string(),
        entity_type: EntityType::Organization,
        description: Some("Test Get Description".to_string()),
        attributes: None,
        schema_id: None,
    };
    
    let request = app
        .authenticated_request("POST", "/api/entities")
        .await
        .json(&create_request);
    
    let (_, created_entity): (_, EntityResponse) = app.send_request(request).await;
    
    // Now try to get the entity
    let request = app
        .authenticated_request(
            "GET",
            &format!("/api/entities/{}", created_entity.id),
        )
        .await;
    
    let (status, response): (_, EntityResponse) = app.send_request(request).await;
    
    // Assert response
    assert_eq!(status, 200);
    assert_eq!(response.id, created_entity.id);
    assert_eq!(response.name, "Test Get Entity");
    assert_eq!(response.entity_type, EntityType::Organization);
}

#[tokio::test]
async fn test_update_entity() {
    // Setup test app
    let app = TestApp::new().await;
    
    // First create an entity
    let create_request = CreateEntityRequest {
        name: "Test Update Entity".to_string(),
        entity_type: EntityType::Person,
        description: Some("Original Description".to_string()),
        attributes: None,
        schema_id: None,
    };
    
    let request = app
        .authenticated_request("POST", "/api/entities")
        .await
        .json(&create_request);
    
    let (_, created_entity): (_, EntityResponse) = app.send_request(request).await;
    
    // Update the entity
    let update_request = UpdateEntityRequest {
        name: Some("Updated Entity Name".to_string()),
        description: Some("Updated Description".to_string()),
        entity_type: Some(EntityType::Organization),
        attributes: None,
    };
    
    let request = app
        .authenticated_request(
            "PUT",
            &format!("/api/entities/{}", created_entity.id),
        )
        .await
        .json(&update_request);
    
    let (status, response): (_, EntityResponse) = app.send_request(request).await;
    
    // Assert response
    assert_eq!(status, 200);
    assert_eq!(response.id, created_entity.id);
    assert_eq!(response.name, "Updated Entity Name");
    assert_eq!(response.entity_type, EntityType::Organization);
    assert_eq!(response.description, Some("Updated Description".to_string()));
}

#[tokio::test]
async fn test_delete_entity() {
    // Setup test app
    let app = TestApp::new().await;
    
    // First create an entity
    let create_request = CreateEntityRequest {
        name: "Test Delete Entity".to_string(),
        entity_type: EntityType::Person,
        description: Some("To be deleted".to_string()),
        attributes: None,
        schema_id: None,
    };
    
    let request = app
        .authenticated_request("POST", "/api/entities")
        .await
        .json(&create_request);
    
    let (_, created_entity): (_, EntityResponse) = app.send_request(request).await;
    
    // Delete the entity
    let request = app
        .authenticated_request(
            "DELETE",
            &format!("/api/entities/{}", created_entity.id),
        )
        .await;
    
    let response = app.router().oneshot(request).await.unwrap();
    
    // Assert response
    assert_eq!(response.status(), 204);
    
    // Try to get the deleted entity (should fail)
    let request = app
        .authenticated_request(
            "GET",
            &format!("/api/entities/{}", created_entity.id),
        )
        .await;
    
    let response = app.router().oneshot(request).await.unwrap();
    assert_eq!(response.status(), 404);
}

#[tokio::test]
async fn test_list_entities() {
    // Setup test app
    let app = TestApp::new().await;
    
    // Create some test entities
    for i in 0..5 {
        let create_request = CreateEntityRequest {
            name: format!("Test Entity {}", i),
            entity_type: if i % 2 == 0 {
                EntityType::Person
            } else {
                EntityType::Organization
            },
            description: Some(format!("Test Description {}", i)),
            attributes: None,
            schema_id: None,
        };
        
        let request = app
            .authenticated_request("POST", "/api/entities")
            .await
            .json(&create_request);
        
        let _: EntityResponse = app.send_request(request).await.1;
    }
    
    // List entities with pagination
    let request = app
        .authenticated_request("GET", "/api/entities?page=1&per_page=3")
        .await;
    
    let (status, response): (_, PaginatedResponse<EntityResponse>) = app.send_request(request).await;
    
    // Assert response
    assert_eq!(status, 200);
    assert_eq!(response.items.len(), 3);
    assert_eq!(response.total, 5);
    assert_eq!(response.page, 1);
    assert_eq!(response.per_page, 3);
    assert_eq!(response.total_pages, 2);
}
