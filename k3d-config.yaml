# k3d cluster configuration for Warda development
apiVersion: k3d.io/v1alpha5
kind: Simple
metadata:
  name: warda-dev
servers: 1
agents: 0
kubeAPI:
  host: "0.0.0.0"
  hostIP: "127.0.0.1"
  hostPort: "6550"
image: rancher/k3s:v1.31.5-k3s1
ports:
  - port: 30090:30090
    nodeFilters:
      - loadbalancer
  - port: 30091:30091
    nodeFilters:
      - loadbalancer
  - port: 30092:30092
    nodeFilters:
      - loadbalancer
registries:
  create:
    name: warda-dev-registry
    host: "0.0.0.0"
    hostPort: "5001"
options:
  k3d:
    wait: true
    timeout: "60s"
    disableLoadbalancer: false
  k3s:
    extraArgs:
      - arg: --disable=traefik
        nodeFilters:
          - server:*
  kubeconfig:
    updateDefaultKubeconfig: true
    switchCurrentContext: true
