#!/bin/sh

# Substitute environment variables in HTML files
echo "Substituting environment variables..."

# Default values if not set
# For local development, use localhost with port forwarding
KEYCLOAK_URL=${KEYCLOAK_URL:-"http://localhost:8080"}
KEYCLOAK_REALM=${KE<PERSON>CLOAK_REALM:-"warda"}
KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID:-"warda-frontend"}

echo "Using Keycloak URL: $KEYCLOAK_URL"
echo "Using Keycloak Realm: $KEYCLOAK_REALM"
echo "Using Keycloak Client ID: $KEYCLOAK_CLIENT_ID"

# Substitute variables in all HTML files
find /usr/share/nginx/html -name "*.html" -exec sed -i "
    s|\${KEYCLOAK_URL}|$KEYCLOAK_URL|g;
    s|\${KEYCLOAK_REALM}|$KEYCLOAK_REALM|g;
    s|\${KEYCLOAK_CLIENT_ID}|$KEYCLOAK_CLIENT_ID|g
" {} \;

echo "Environment variable substitution complete."

# Start nginx
exec nginx -g "daemon off;"
