server {
    listen 80;
    server_name localhost;

    # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # Set root directory
    root /usr/share/nginx/html;
    index index.html;

    # Set default MIME type to text/html
    default_type text/html;

    # MIME types
    types {
        text/html                html htm shtml;
        text/css                 css;
        text/javascript          js;
        application/javascript   js mjs;
        application/json         json;
        application/wasm         wasm;
        image/svg+xml            svg svgz;
        application/x-font-ttf    ttf;
        font/woff2               woff2;
        image/png                png;
        image/jpeg               jpg jpeg;
        application/octet-stream bin exe dll;
    }

    # Handle all requests
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Set MIME types for WebAssembly files
    types {
        application/wasm wasm;
    }

    # Cache static assets
    location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|wasm)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public";
    }

    location ~* \.(?:css|js)$ {
        expires 7d;
        access_log off;
        add_header Cache-Control "public";
    }

    # Don't cache HTML files
    location ~* \.(?:html|htm)$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }
}
