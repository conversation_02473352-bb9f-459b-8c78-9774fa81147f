FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy all source files
COPY . .

# Build the backend directly
RUN cargo build --release --package backend && \
    cp target/release/backend /usr/local/bin/backend

FROM debian:bookworm-slim AS runtime

RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

COPY --from=builder /usr/local/bin/backend /usr/local/bin/backend

ENTRYPOINT ["/usr/local/bin/backend"]