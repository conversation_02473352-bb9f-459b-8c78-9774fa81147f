-- Create Keycloak database and user with proper permissions
-- This migration sets up the complete database infrastructure for Keycloak
-- Note: This should be run with superuser privileges

-- Create keycloak user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'keycloak') THEN
        CREATE USER keycloak WITH PASSWORD 'keycloak_password';
        RAISE NOTICE 'Created keycloak user';
    ELSE
        RAISE NOTICE 'Keycloak user already exists';
    END IF;
END
$$;

-- Create keycloak database if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'keycloak') THEN
        -- Note: CREATE DATABASE cannot be executed inside a transaction block
        -- This will be handled by the init container script
        RAISE NOTICE 'Keycloak database creation will be handled by init container';
    ELSE
        RAISE NOTICE 'Keycloak database already exists';
    END IF;
END
$$;

-- Grant comprehensive privileges (to be run after database creation)
-- These commands should be executed in the keycloak database context

-- Grant database ownership and privileges
-- ALTER DATABASE keycloak OWNER TO keycloak;
-- GRANT ALL PRIVILEGES ON DATABASE keycloak TO keycloak;
-- GRANT CONNECT ON DATABASE keycloak TO keycloak;

-- Grant comprehensive schema-level privileges
-- GRANT ALL ON SCHEMA public TO keycloak;
-- ALTER SCHEMA public OWNER TO keycloak;

-- Grant privileges on existing objects
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO keycloak;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO keycloak;
-- GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO keycloak;

-- Set default privileges for future objects
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO keycloak;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO keycloak;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO keycloak;

-- Grant extension creation privileges
-- GRANT CREATE ON DATABASE keycloak TO keycloak;

-- Create a table to track Keycloak setup status
CREATE TABLE IF NOT EXISTS keycloak_setup_status (
    id SERIAL PRIMARY KEY,
    setup_completed BOOLEAN DEFAULT FALSE,
    setup_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

-- Insert initial status if not exists
INSERT INTO keycloak_setup_status (setup_completed, notes)
SELECT FALSE, 'Database and user infrastructure prepared for Keycloak'
WHERE NOT EXISTS (SELECT 1 FROM keycloak_setup_status);

-- Note: The actual keycloak database and privilege grants will be handled by the init container
-- that runs with superuser privileges before Keycloak starts
