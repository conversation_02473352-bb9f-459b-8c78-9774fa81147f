# Global configuration
global:
  # Image configuration
  image:
    registry: "warda-dev-registry:5001"
    repository: warda
    tag: latest
    pullPolicy: Always

  # Cluster configuration
  # For local development: use "localhost"
  # For remote cluster: use your cluster IP (e.g., "*************")
  clusterHost: "localhost"

  # NodePort configuration for k3d
  nodePort:
    frontend: 30090
    backend: 30091
    keycloak: 30092

  # Database configuration
  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

# Keycloak configuration
keycloak:
  enabled: true
  image:
    repository: quay.io/keycloak/keycloak
    tag: "23.0.0"
    pullPolicy: IfNotPresent

  admin:
    username: admin
    password: admin123

  database:
    host: warda-postgresql
    port: 5432
    name: keycloak
    username: keycloak
    password: keycloak_password

  hostname: "localhost"

  service:
    type: NodePort
    nodePort: 30092

  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

  realm:
    name: warda
    displayName: "Warda Platform"
    enabled: true
    frontendClient:
      clientId: warda-frontend
      name: "Warda Frontend"
      enabled: true
      publicClient: true
      directAccessGrantsEnabled: false
      standardFlowEnabled: true
      implicitFlowEnabled: false
      serviceAccountsEnabled: false
      # Include both web and native redirect URIs
      redirectUris:
        - "http://localhost:30090/*"
        - "http://localhost:8888/callback"
      webOrigins:
        - "http://localhost:30090"
    backendClient:
      clientId: warda-backend
      name: "Warda Backend"
      enabled: true
      publicClient: false
      directAccessGrantsEnabled: true
      standardFlowEnabled: true
      serviceAccountsEnabled: true
      secret: backend-client-secret
      # redirectUris will be generated from global.clusterHost

frontend:
  enabled: true
  image:
    repository: warda-dev-registry:5001/warda/frontend
    tag: latest
    pullPolicy: Always

  service:
    type: NodePort
    nodePort: 30090

  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"

  keycloak:
    # URL will be dynamically set in the frontend deployment template
    realm: "warda"
    clientId: "warda-frontend"

backend:
  enabled: true
  image:
    repository: warda-dev-registry:5001/warda/backend
    tag: latest
    pullPolicy: Always

  service:
    type: NodePort
    nodePort: 30091

  databaseUrl: "***********************************************************/postgresql"

  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"

  database:
    host: "warda-postgresql"
    port: 5432
    name: postgresql
    user: postgres

  oauth:
    keycloak:
      url: "http://warda-keycloak:8080"
      realm: "warda"
      clientId: "warda-backend"
      issuer: "http://warda-keycloak:8080/realms/warda"

migrate:
  enabled: true
  image:
    repository: warda-dev-registry:5001/warda/migrate
    tag: latest
    pullPolicy: Always

  waitContainer:
    image: alpine
    tag: "3.18"