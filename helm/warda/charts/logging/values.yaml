loki-stack:
  loki:
    enabled: true
  promtail:
    enabled: true
    config:
      clients:
        - url: http://warda-loki:3100/loki/api/v1/push
      snippets:
        pipeline_stages:
          - json:
              expressions:
                level: level
                timestamp: timestamp
                msg: fields.message
                id: fields.id
                name: fields.name
          - labels:
              level:
              app:
  grafana:
    enabled: true
    adminPassword: admin  # Optional override
    service:
      type: NodePort
    ingress:
      enabled: false
    sidecar:
      dashboards:
        enabled: true
        label: grafana_dashboard
        folder: /tmp/dashboards
        initDatasources: false  # disables immediate POST reload, avoids race condition

    persistence:
      enabled: false
  grafana.ini:
    dashboards:
      default_home_dashboard_path: /tmp/dashboards/pods-logs.json
    auth.anonymous:
      enabled: true
