# Default values for keycloak.
# This is a YAML-formatted file.
# Most configuration is passed from the parent chart's values.yaml

# Replica count
replicaCount: 1

# Image configuration
image:
  repository: quay.io/keycloak/keycloak
  tag: "23.0.0"
  pullPolicy: IfNotPresent

# Init container configuration
initContainer:
  image: postgres:15-alpine

# Service configuration
service:
  type: NodePort
  port: 8080
  targetPort: 8080
  nodePort: 30092

# Service account configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Image pull secrets
imagePullSecrets: []

# Pod configuration
podAnnotations: {}
podLabels: {}

# Security context
podSecurityContext: {}
securityContext: {}

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

# Readiness and liveness probes
readinessProbe:
  httpGet:
    path: /realms/master
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

livenessProbe:
  httpGet:
    path: /realms/master
    port: 8080
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

# Ingress configuration (defaults - overridden by parent)
ingress:
  enabled: false
  className: ""
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
  hosts:
    - host: keycloak.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Node selector, affinity, and tolerations
nodeSelector: {}
affinity: {}
tolerations: []
