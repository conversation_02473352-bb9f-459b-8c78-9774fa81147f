apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "keycloak.fullname" . }}-init-script
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
data:
  init-keycloak-db.sh: |
    #!/bin/bash
    set -e

    echo "🔧 Initializing Keycloak database setup..."

    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    until pg_isready -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres; do
      echo "PostgreSQL not ready, waiting..."
      sleep 2
    done

    echo "✅ PostgreSQL is ready"

    # Check if keycloak database exists, create if not
    echo "🔍 Checking if keycloak database exists..."
    if ! psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -lqt | cut -d \| -f 1 | grep -qw {{ .Values.database.name }}; then
      echo "📦 Creating keycloak database..."
      psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "CREATE DATABASE {{ .Values.database.name }};"
      echo "✅ Keycloak database created"
    else
      echo "✅ Keycloak database already exists"
    fi

    # Check if keycloak user exists, create if not
    echo "🔍 Checking if keycloak user exists..."
    if ! psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -t -c "SELECT 1 FROM pg_roles WHERE rolname='{{ .Values.database.username }}'" | grep -q 1; then
      echo "👤 Creating keycloak user..."
      psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "CREATE USER {{ .Values.database.username }} WITH PASSWORD '{{ .Values.database.password }}';"
      echo "✅ Keycloak user created"
    else
      echo "✅ Keycloak user already exists"
    fi

    # Grant database ownership and privileges
    echo "🔐 Granting database ownership to keycloak user..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "ALTER DATABASE {{ .Values.database.name }} OWNER TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE {{ .Values.database.name }} TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "GRANT CONNECT ON DATABASE {{ .Values.database.name }} TO {{ .Values.database.username }};"

    # Grant comprehensive schema-level privileges
    echo "🔐 Granting comprehensive schema-level privileges to keycloak user..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "GRANT ALL ON SCHEMA public TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "ALTER SCHEMA public OWNER TO {{ .Values.database.username }};"

    # Grant privileges on existing objects
    echo "🔐 Granting privileges on existing database objects..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO {{ .Values.database.username }};"

    # Set default privileges for future objects
    echo "🔐 Setting default privileges for future objects..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO {{ .Values.database.username }};"

    # Ensure the user can create extensions if needed
    echo "🔐 Granting extension creation privileges..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -d {{ .Values.database.name }} -c "GRANT CREATE ON DATABASE {{ .Values.database.name }} TO {{ .Values.database.username }};"

    echo "✅ Keycloak database initialization complete"

  import-realm.sh: |
    #!/bin/bash
    set -e

    echo "🏰 Starting Keycloak realm import process..."

    # Install curl if not available
    if ! command -v curl &> /dev/null; then
      echo "📦 Installing curl..."
      apk add --no-cache curl
    fi

    KEYCLOAK_URL="http://${KEYCLOAK_SERVICE_NAME:-localhost}:8080"
    REALM_NAME="{{ .Values.realm.name | default "warda" }}"
    ADMIN_USER="$KEYCLOAK_ADMIN"
    ADMIN_PASS="$KEYCLOAK_ADMIN_PASSWORD"
    REALM_FILE="/realm-config/warda-realm.json"

    # Wait for Keycloak to be ready
    echo "⏳ Waiting for Keycloak to be ready..."
    for i in {1..120}; do
      if curl -s -f "$KEYCLOAK_URL/realms/master" > /dev/null 2>&1; then
        echo "✅ Keycloak is ready"
        # Give Keycloak a bit more time to fully initialize
        sleep 10
        break
      fi
      echo "Waiting for Keycloak... (attempt $i/120)"
      sleep 5
    done

    # Check if we can reach Keycloak
    if ! curl -s -f "$KEYCLOAK_URL/realms/master" > /dev/null 2>&1; then
      echo "❌ Failed to connect to Keycloak after 10 minutes"
      exit 1
    fi

    # Get admin token
    echo "🔑 Getting admin token..."
    TOKEN_RESPONSE=$(curl -s -X POST "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=$ADMIN_USER" \
      -d "password=$ADMIN_PASS" \
      -d "grant_type=password" \
      -d "client_id=admin-cli")

    if [ $? -ne 0 ]; then
      echo "❌ Failed to get admin token"
      exit 1
    fi

    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

    if [ -z "$ACCESS_TOKEN" ]; then
      echo "❌ Failed to extract access token"
      echo "Response: $TOKEN_RESPONSE"
      exit 1
    fi

    echo "✅ Got admin token"

    # Check if realm already exists
    echo "🔍 Checking if realm '$REALM_NAME' exists..."
    REALM_CHECK=$(curl -s -o /dev/null -w "%{http_code}" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      "$KEYCLOAK_URL/admin/realms/$REALM_NAME")

    if [ "$REALM_CHECK" = "200" ]; then
      echo "✅ Realm '$REALM_NAME' already exists, skipping import"
      exit 0
    fi

    # Import realm
    echo "📦 Importing realm '$REALM_NAME'..."
    IMPORT_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/import_response.txt \
      -X POST "$KEYCLOAK_URL/admin/realms" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: application/json" \
      -d @"$REALM_FILE")

    HTTP_CODE="${IMPORT_RESPONSE: -3}"

    if [ "$HTTP_CODE" = "201" ]; then
      echo "✅ Realm '$REALM_NAME' imported successfully"
    else
      echo "❌ Failed to import realm. HTTP code: $HTTP_CODE"
      echo "Response:"
      cat /tmp/import_response.txt
      exit 1
    fi

    echo "🎉 Realm import process completed successfully!"
