apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-secrets
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
stringData:
  # Admin credentials
  KEYCLOAK_ADMIN: {{ .Values.admin.username | default "admin" | quote }}
  KEYCLOAK_ADMIN_PASSWORD: {{ .Values.admin.password | default "admin123" | quote }}

  # Database credentials and connection
  KC_DB_USERNAME: {{ .Values.database.username | default "keycloak" | quote }}
  KC_DB_PASSWORD: {{ .Values.database.password | default "keycloak_password" | quote }}
  KC_DB_URL_DATABASE: {{ .Values.database.name | default "keycloak" | quote }}
  KC_DB_URL: {{ printf "jdbc:postgresql://%s:%v/%s" (.Values.database.host | default "warda-postgresql") (.Values.database.port | default 5432) (.Values.database.name | default "keycloak") | quote }}

  # Backend client secret
  BACKEND_CLIENT_SECRET: {{ .Values.realm.backendClient.secret | default "backend-client-secret" | quote }}
