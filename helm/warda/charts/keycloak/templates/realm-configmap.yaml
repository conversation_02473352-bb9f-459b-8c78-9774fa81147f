apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "keycloak.fullname" . }}-realm-config
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
data:
  warda-realm.json: |
    {
      "realm": "{{ .Values.realm.name | default "warda" }}",
      "displayName": "{{ .Values.realm.displayName | default "Warda Platform" }}",
      "enabled": {{ .Values.realm.enabled | default true }},
      "sslRequired": "none",
      "registrationAllowed": true,
      "loginWithEmailAllowed": true,
      "duplicateEmailsAllowed": false,
      "resetPasswordAllowed": true,
      "editUsernameAllowed": true,
      "bruteForceProtected": true,
      "clients": [
        {{- if .Values.realm.frontendClient }}
        {
          "clientId": "{{ .Values.realm.frontendClient.clientId | default "warda-frontend" }}",
          "name": "{{ .Values.realm.frontendClient.name | default "Warda Frontend" }}",
          "enabled": {{ .Values.realm.frontendClient.enabled | default true }},
          "publicClient": {{ .Values.realm.frontendClient.publicClient | default true }},
          "directAccessGrantsEnabled": {{ .Values.realm.frontendClient.directAccessGrantsEnabled | default false }},
          "standardFlowEnabled": {{ .Values.realm.frontendClient.standardFlowEnabled | default true }},
          "implicitFlowEnabled": {{ .Values.realm.frontendClient.implicitFlowEnabled | default false }},
          "serviceAccountsEnabled": {{ .Values.realm.frontendClient.serviceAccountsEnabled | default false }},
          "redirectUris": {{ if .Values.realm.frontendClient.redirectUris }}{{ .Values.realm.frontendClient.redirectUris | toJson }}{{ else }}{{ list (printf "http://%s:%v/*" (.Values.global.clusterHost | default "localhost") (.Values.global.nodePort.frontend | default 30090)) | toJson }}{{ end }},
          "webOrigins": {{ if .Values.realm.frontendClient.webOrigins }}{{ .Values.realm.frontendClient.webOrigins | toJson }}{{ else }}{{ list (printf "http://%s:%v" (.Values.global.clusterHost | default "localhost") (.Values.global.nodePort.frontend | default 30090)) | toJson }}{{ end }},
          "protocol": "openid-connect",
          "attributes": {
            "post.logout.redirect.uris": "+"
          }
        }{{- if .Values.realm.backendClient }},{{- end }}
        {{- end }}
        {{- if .Values.realm.backendClient }}
        {
          "clientId": "{{ .Values.realm.backendClient.clientId | default "warda-backend" }}",
          "name": "{{ .Values.realm.backendClient.name | default "Warda Backend" }}",
          "enabled": {{ .Values.realm.backendClient.enabled | default true }},
          "publicClient": {{ .Values.realm.backendClient.publicClient | default false }},
          "directAccessGrantsEnabled": {{ .Values.realm.backendClient.directAccessGrantsEnabled | default true }},
          "standardFlowEnabled": {{ .Values.realm.backendClient.standardFlowEnabled | default true }},
          "serviceAccountsEnabled": {{ .Values.realm.backendClient.serviceAccountsEnabled | default true }},
          "secret": "{{ .Values.realm.backendClient.secret | default "backend-client-secret" }}",
          "redirectUris": {{ if .Values.realm.backendClient.redirectUris }}{{ .Values.realm.backendClient.redirectUris | toJson }}{{ else }}{{ list (printf "http://%s:%v/*" (.Values.global.clusterHost | default "localhost") (.Values.global.nodePort.backend | default 30091)) | toJson }}{{ end }},
          "protocol": "openid-connect",
          "bearerOnly": false,
          "consentRequired": false,
          "frontchannelLogout": true
        }
        {{- end }}
      ],
      "roles": {
        "realm": [
          {
            "name": "user",
            "description": "User role"
          },
          {
            "name": "admin",
            "description": "Administrator role"
          }
        ]
      },
      "users": [
        {
          "username": "testuser",
          "enabled": true,
          "email": "<EMAIL>",
          "firstName": "Test",
          "lastName": "User",
          "credentials": [
            {
              "type": "password",
              "value": "password",
              "temporary": false
            }
          ],
          "realmRoles": ["user"]
        }
      ]
    }
