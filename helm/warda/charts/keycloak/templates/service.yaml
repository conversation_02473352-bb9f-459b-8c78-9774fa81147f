apiVersion: v1
kind: Service
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
      {{- if (and (eq .Values.service.type "NodePort") .Values.service.nodePort) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    {{- include "keycloak.selectorLabels" . | nindent 4 }}
