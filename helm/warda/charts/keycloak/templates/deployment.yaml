apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "keycloak.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "keycloak.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: init-keycloak-db
          image: {{ .Values.initContainer.image }}
          command:
            - /bin/bash
            - /scripts/init-keycloak-db.sh
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-password
          volumeMounts:
            - name: init-script
              mountPath: /scripts
              readOnly: true

      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - start-dev
            - --db=postgres
          env:
            # Non-sensitive environment variables
            - name: KC_DB
              value: "postgres"
            - name: KC_HOSTNAME_URL
              value: "http://{{ .Values.global.clusterHost | default .Values.hostname | default "keycloak.local" }}:{{ .Values.global.nodePort.keycloak | default 30092 }}"
            - name: KC_HOSTNAME_STRICT
              value: "false"
            - name: KC_HTTP_ENABLED
              value: "true"
            - name: KC_PROXY
              value: "edge"
            # Sensitive environment variables from secrets
            - name: KEYCLOAK_ADMIN
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KEYCLOAK_ADMIN
            - name: KEYCLOAK_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KEYCLOAK_ADMIN_PASSWORD
            - name: KC_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KC_DB_USERNAME
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KC_DB_PASSWORD
            - name: KC_DB_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KC_DB_URL
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: init-script
          configMap:
            name: {{ include "keycloak.fullname" . }}-init-script
            defaultMode: 0755
        - name: realm-config
          configMap:
            name: {{ include "keycloak.fullname" . }}-realm-config
