apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-backend
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  selector:
    app.kubernetes.io/name: backend
    app.kubernetes.io/instance: {{ .Release.Name }}
  ports:
    - protocol: TCP
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      name: http
      {{- if (and (eq .Values.service.type "NodePort") .Values.service.nodePort) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}