# Default values for the migrate chart.
# This is a YAML-formatted file.

# Image configuration for the migration container
image:
  repository: warda/migrate
  tag: latest
  pullPolicy: IfNotPresent

# Configuration for the wait-for-postgres init container
waitContainer:
  image: alpine
  tag: "3.18"

# Database configuration will be set by the parent chart
databaseUrl: ""

# Pod configuration
podAnnotations: {}
podLabels: {}

# Node selector for pod assignment
nodeSelector: {}

# Tolerations for pod assignment
tolerations: []

# Affinity for pod assignment
affinity: {}

# Resources for the migrate container
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
