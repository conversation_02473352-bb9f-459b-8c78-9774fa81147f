apiVersion: v1
kind: Secret
metadata:
  name: {{ include "postgresql.fullname" . }}
  labels:
    app.kubernetes.io/name: postgresql
    helm.sh/chart: postgresql-0.1.0
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/component: database
type: Opaque
stringData:
  postgres-password: {{ .Values.postgresqlPassword | quote }}
  postgres-username: {{ .Values.postgresqlUsername | quote }}
  postgres-db: {{ .Values.postgresqlDatabase | quote }}
  database-url: postgresql://{{ .Values.postgresqlUsername }}:{{ .Values.postgresqlPassword }}@{{ include "postgresql.fullname" . }}:{{ .Values.service.port }}/{{ .Values.postgresqlDatabase }}
