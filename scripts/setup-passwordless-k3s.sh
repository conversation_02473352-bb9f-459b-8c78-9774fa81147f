#!/bin/bash
set -e

echo "🔧 Setting up passwordless sudo for k3s commands..."

# Get the current user (should be the GitHub Actions runner user)
RUNNER_USER=$(whoami)
echo "Setting up for user: $RUNNER_USER"

# Add sudoers rule for k3s commands
sudo tee /etc/sudoers.d/k3s-runner > /dev/null <<EOF
# Allow GitHub Actions runner to use k3s commands without password
$RUNNER_USER ALL=(ALL) NOPASSWD: /usr/local/bin/k3s
$RUNNER_USER ALL=(ALL) NOPASSWD: /usr/local/bin/k3s ctr *
EOF

# Set proper permissions
sudo chmod 440 /etc/sudoers.d/k3s-runner

# Test the configuration
echo "🧪 Testing passwordless sudo for k3s..."
if sudo k3s version >/dev/null 2>&1; then
    echo "✅ Passwordless sudo for k3s is working!"
else
    echo "❌ Passwordless sudo setup failed"
    exit 1
fi

echo "🎉 Setup complete! GitHub Actions can now use k3s commands without password prompts."
