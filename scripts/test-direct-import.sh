#!/bin/bash
set -e

echo "🧪 Testing direct image import to k3s..."

# Build a test image
echo "🛠️ Building test image..."
docker build -t warda/test:latest - <<EOF
FROM alpine:latest
RUN echo "Hello from Warda test image" > /hello.txt
CMD cat /hello.txt
EOF

# Import to k3s
echo "📥 Importing test image to k3s..."
docker save warda/test:latest | sudo k3s ctr images import -

# Verify image is in k3s
echo "🔍 Checking if image is available in k3s..."
if sudo k3s ctr images list | grep -q "warda/test:latest"; then
    echo "✅ Image found in k3s image store"
else
    echo "❌ Image not found in k3s"
    exit 1
fi

# Test running the image in k3s
echo "🚀 Testing image in k3s..."
kubectl run test-direct-import --rm -i --restart=Never --image=warda/test:latest --image-pull-policy=Never -- cat /hello.txt

echo "🎉 Direct import test completed successfully!"
echo ""
echo "💡 This confirms that:"
echo "  - Images can be built locally"
echo "  - Images can be imported directly to k3s"
echo "  - k3s can run imported images with pullPolicy=Never"
